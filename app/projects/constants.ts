import { Project } from './types';

export const projects: Project[] = [
  {
    title: "Email Response Agent",
    src: "email-automation.jpg",
    color: "#4F46E5",
    slug: "email-response-agent",
    role: "AI-Powered Email Automation",
    description: "Intelligent email categorization and automated response system using AI. Monitors Gmail inbox, categorizes emails by urgency and type, and generates contextual responses using advanced language models. Features both simple and advanced workflows for different complexity needs.",
    technologies: ["n8n", "Gmail API", "OpenAI", "LangChain", "AI Agents"],
    category: "Automation",
    setupGuide: "/projects/Email_n8n/Setup Guide.pdf",
    jsonFile: "/projects/Email_n8n/Email Response Agent (Advanced).json"
  },
  {
    title: "FounderOS - Meta Ads Automation",
    src: "founder-os.jpg",
    color: "#059669",
    slug: "founder-os",
    role: "Complete Ad Campaign Automation",
    description: "End-to-end Meta ads automation system that researches products, generates compelling ad angles, creates video sales letter scripts, tracks performance metrics, and identifies winning patterns. Integrates Airtable, n8n, Perplexity AI, and Meta API for complete campaign management.",
    technologies: ["n8n", "Airtable", "Meta API", "Perplexity AI", "Gemini 2.5 Pro", "OpenAI", "ScrapeFly"],
    category: "Marketing Automation",
    setupGuide: "/projects/Founder_n8n/FounderOS_SetupGuide.txt",
    jsonFile: "/projects/Founder_n8n/FounderOS.json"
  },
  {
    title: "Keyword Research Automation",
    src: "keyword-research.jpg",
    color: "#DC2626",
    slug: "keyword-research",
    role: "SEO Keyword Intelligence",
    description: "Automated keyword research and clustering system that discovers high-value keywords, analyzes search volumes, and groups related terms using AI. Streamlines SEO research process with intelligent filtering and categorization for content strategy optimization.",
    technologies: ["n8n", "Keyword APIs", "OpenAI", "Data Processing", "SEO Tools"],
    category: "SEO Automation",
    setupGuide: "/projects/Keyword_n8n/keyword research workflow.pdf",
    jsonFile: "/projects/Keyword_n8n/keyword_research_workflow.json"
  },
  {
    title: "SEO Blog Automation",
    src: "seo-automation.jpg",
    color: "#7C3AED",
    slug: "seo-blog-automation",
    role: "Content Generation & Publishing",
    description: "Fully automated blog content creation and publishing system. Researches topics using Tavily, generates SEO-optimized articles with AI, creates featured images, and publishes directly to WordPress. Includes metadata optimization and image processing for complete content automation.",
    technologies: ["n8n", "WordPress API", "OpenAI", "Tavily", "Image Processing", "SEO"],
    category: "Content Automation",
    setupGuide: "/projects/SEO_n8n/Setup Guide.pdf",
    jsonFile: "/projects/SEO_n8n/SEO_Blog_automation.json"
  },
  {
    title: "YouTube Content Automation",
    src: "youtube-automation.jpg",
    color: "#EA580C",
    slug: "youtube-automation",
    role: "Faceless Video Creation & Publishing",
    description: "Complete faceless YouTube automation system that generates video content, creates thumbnails, produces videos using AI, and publishes to YouTube. Integrates with Google Sheets for content planning and Runway ML for video generation, enabling hands-off content creation.",
    technologies: ["n8n", "YouTube API", "Runway ML", "Google Sheets", "AI Video Generation", "Automation"],
    category: "Video Automation",
    setupGuide: "/projects/Youtube_n8n/YouTube_Content_Automation_(Faceless)_.pdf",
    jsonFile: "/projects/Youtube_n8n/YouTube_Automation.json"
  }
];
