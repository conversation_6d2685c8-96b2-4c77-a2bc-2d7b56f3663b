import { projects } from './constants';
import { Project } from './types';

export function getProjectBySlug(slug: string): Project | undefined {
  return projects.find(project => project.slug === slug);
}

export function getAllProjects(): Project[] {
  return projects;
}

export function getProjectsByCategory(category: string): Project[] {
  return projects.filter(project => project.category === category);
}

export function getProjectCategories(): string[] {
  const categories = projects
    .map(project => project.category)
    .filter((category): category is string => Boolean(category));

  return Array.from(new Set(categories));
}

// Helper function to get the folder name for a project
export function getProjectFolderName(slug: string): string {
  const folderMap: Record<string, string> = {
    // Project folder mappings will be added here when new projects are created
  };

  return folderMap[slug] || slug.replace('-', '_') + '_n8n';
}

// Helper function to get the download URL for a project's JSON file
export function getProjectDownloadUrl(project: Project): string | null {
  if (!project.jsonFile || !project.slug) {
    return null;
  }

  // Projects will need to specify their own download URLs when added
  // This can be updated based on where project files are hosted
  return null;
}
